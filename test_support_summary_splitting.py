#!/usr/bin/env python3
"""
Test script for the support summary message splitting functionality.
"""

import sys
import os

# Add the app directory to the path so we can import the module
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.support_summary_command import split_message_preserving_codeblocks, SLACK_MESSAGE_LIMIT


def test_short_message():
    """Test that short messages are not split."""
    message = "This is a short message"
    result = split_message_preserving_codeblocks(message)
    assert len(result) == 1
    assert result[0] == message
    print("✓ Short message test passed")


def test_long_message_without_codeblocks():
    """Test that long messages without code blocks are split properly."""
    # Create a message longer than the limit
    long_line = "A" * 100
    lines = [long_line for _ in range(50)]  # 5000 characters total
    message = "\n".join(lines)
    
    result = split_message_preserving_codeblocks(message)
    
    # Should be split into multiple chunks
    assert len(result) > 1
    
    # Each chunk should be under the limit
    for chunk in result:
        assert len(chunk) <= SLACK_MESSAGE_LIMIT
    
    # When joined back together (minus any added ```), should contain all original content
    combined = "\n".join(result)
    # Count original A's vs combined A's
    original_a_count = message.count('A')
    combined_a_count = combined.count('A')
    assert original_a_count == combined_a_count
    
    print("✓ Long message without code blocks test passed")


def test_message_with_codeblocks():
    """Test that messages with code blocks preserve formatting."""
    # Create a message with code blocks that exceeds the limit
    header = "*Support Summary:*"
    code_start = "```"
    code_lines = []
    
    # Create enough lines to exceed the limit
    for i in range(200):
        code_lines.append(f"R{i:03d}  In Progress  Some issue summary here  CRS-{i:04d}")
    
    code_content = "\n".join(code_lines)
    code_end = "```"
    
    message = f"{header}\n{code_start}\n{code_content}\n{code_end}"
    
    result = split_message_preserving_codeblocks(message)
    
    # Should be split into multiple chunks
    assert len(result) > 1
    print(f"Message split into {len(result)} chunks")
    
    # Each chunk should be under the limit
    for i, chunk in enumerate(result):
        print(f"Chunk {i+1} length: {len(chunk)}")
        assert len(chunk) <= SLACK_MESSAGE_LIMIT
    
    # First chunk should start with header and code block
    assert result[0].startswith(header)
    assert "```" in result[0]
    
    # Last chunk should end with code block close
    assert result[-1].endswith("```")
    
    # Middle chunks (if any) should start and end with ```
    for chunk in result[1:-1]:
        assert chunk.startswith("```")
        assert chunk.endswith("```")
    
    # All chunks except first should start with ```
    for chunk in result[1:]:
        assert chunk.startswith("```")
    
    # All chunks except last should end with ```
    for chunk in result[:-1]:
        assert chunk.endswith("```")
    
    print("✓ Message with code blocks test passed")


def test_edge_case_exact_limit():
    """Test edge case where message is exactly at the limit."""
    # Create a message exactly at the limit
    message = "A" * SLACK_MESSAGE_LIMIT
    result = split_message_preserving_codeblocks(message)
    
    assert len(result) == 1
    assert result[0] == message
    print("✓ Exact limit test passed")


def test_edge_case_just_over_limit():
    """Test edge case where message is just over the limit."""
    # Create a message just over the limit
    message = "A" * (SLACK_MESSAGE_LIMIT + 1)
    result = split_message_preserving_codeblocks(message)
    
    assert len(result) > 1
    for chunk in result:
        assert len(chunk) <= SLACK_MESSAGE_LIMIT
    
    # Combined should have same content
    combined = "".join(result)
    assert len(combined) == len(message)
    print("✓ Just over limit test passed")


if __name__ == "__main__":
    print("Testing support summary message splitting...")
    print(f"Slack message limit: {SLACK_MESSAGE_LIMIT}")
    print()
    
    try:
        test_short_message()
        test_long_message_without_codeblocks()
        test_message_with_codeblocks()
        test_edge_case_exact_limit()
        test_edge_case_just_over_limit()
        
        print()
        print("🎉 All tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
