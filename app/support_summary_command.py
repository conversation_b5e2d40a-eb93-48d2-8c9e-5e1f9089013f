"""
Support Summary Command Handler Module

This module handles the /support-summary Slack command for generating
summaries of recently modified Jira support tickets.
"""

import logging
import traceback
from datetime import datetime, timedelta
from typing import Tuple, List, Dict, Any
import re
import pytz

from .jira_client import JiraClient
from .config import SUPPORT_SUMMARY_MAX_ISSUES

logger = logging.getLogger(__name__)


CUSTOM_FIELD_LOOKUP = {
    "implements": "customfield_10255" # List of dicts where "value" is the implement number formatted as "R123"
}


def format_summary_for_display(summary: str, max_length: int = 60) -> str:
    """
    Format issue summary for display in the table.
    
    Args:
        summary: The original summary
        max_length: Maximum length for display
        
    Returns:
        str: Formatted summary
    """
    # Remove robot identifier from the beginning if present
    robot_pattern = r'^[SR]\d+\s*[-:]?\s*'
    summary = re.sub(robot_pattern, '', summary, flags=re.IGNORECASE)
    
    # Truncate if too long
    if len(summary) > max_length:
        summary = summary[:max_length-3] + "..."
    
    return summary


def format_status(status_name: str) -> str:
    """
    Format status for display.
    
    Args:
        status_name: The Jira status name
        
    Returns:
        str: Formatted status
    """
    # Map common Jira statuses to display names
    status_mapping = {
        'done': 'Done',
        'closed': 'Done',
        'resolved': 'Done',
        'in progress': 'In Progress',
        'in review': 'In Review',
        'to do': 'To Do',
        'open': 'Open',
        'new': 'New',
    }
    
    return status_mapping.get(status_name.lower(), status_name)


def format_support_summary(issues: List[Dict[str, Any]], title: str = "Support Summary", max_issues: int = 100) -> str:
    """
    Format the issues into a nice table for Slack display.

    Columns are displayed in order: Implement, Status, Summary, Key
    Issues are sorted by implement (robot) identifier.

    Args:
        issues: List of Jira issue dictionaries
        title: Title for the summary
        max_issues: Maximum number of issues to display before truncating

    Returns:
        str: Formatted summary table with space-aligned columns
    """
    if not issues:
        return f"{title}:\nNo issues found."

    total_issues = len(issues)
    issues_to_process = issues[:max_issues] if total_issues > max_issues else issues

    # First loop: gather all display information
    issue_data = []
    for issue in issues_to_process:
        try:
            fields = issue.get('fields', {})
            key = issue.get('key', 'Unknown')
            summary = fields.get('summary', 'No summary')
            status = fields.get('status', {}).get('name', 'Unknown')

            # Extract robot identifier
            implements = fields.get(CUSTOM_FIELD_LOOKUP["implements"])
            
            if len(implements) == 0:
                implement = "???"
            else:
                implement = implements[0]["value"]
            
            logger.info(f"fields {fields}")

            # Format summary for display
            summary_display = format_summary_for_display(summary)

            # Format status
            status_display = format_status(status)

            issue_data.append({
                'robot_display': implement,
                'summary_display': summary_display,
                'status_display': status_display,
                'key': key
            })

        except Exception as e:
            logger.error(f"Error formatting issue {issue.get('key', 'Unknown')}: {e}")
            traceback.print_exc()
            continue

    if not issue_data:
        return f"{title}:\nNo valid issues found."

    issue_data.sort(key=lambda x: x['robot_display'])

    # Calculate column widths based on the longest value in each column
    # Column order: implement, status, summary, key
    max_implement_width = max(len(item['robot_display']) for item in issue_data)
    max_status_width = max(len(item['status_display']) for item in issue_data)
    max_summary_width = max(len(item['summary_display']) for item in issue_data)

    # Add some padding between columns
    implement_width = max_implement_width + 2
    status_width = max_status_width + 2
    summary_width = max_summary_width + 2

    lines = [f"*{title}:*", "```"]

    for item in issue_data:
        # Format each column with proper spacing
        # Column order: implement, status, summary, key
        implement_part = item['robot_display'].ljust(implement_width)
        status_part = item['status_display'].ljust(status_width)
        summary_part = item['summary_display'].ljust(summary_width)
        key_part = item['key']

        line = f"{implement_part}{status_part}{summary_part}{key_part}"
        lines.append(line)

    # Add truncation message if there are more issues than displayed
    if total_issues > max_issues:
        remaining_count = total_issues - max_issues
        lines.append(f"...and {remaining_count} more issues")

    lines.append("```")
    joined_lines = "\n".join(lines)

    return joined_lines


def get_time_period_from_command(command_text: str = "") -> Tuple[datetime, str]:
    """
    Determine the time period based on configurable shift change times.
    Returns issues updated since the previous shift change.

    The SHIFT_TIMES array can be modified to support any number of shifts per day.
    Currently configured for 2 shifts: 8AM and 8PM PST.

    Args:
        command_text: The text following the /support-summary command (unused - kept for compatibility)

    Returns:
        tuple: (since_datetime, period_description)
    """
    # Get current time in PST
    pst = pytz.timezone('US/Pacific')
    now = datetime.now(pst)

    # Configurable shift change times (24-hour format)
    # Examples:
    # [8, 20] = 2 shifts: 8AM-8PM, 8PM-8AM
    # [6, 14, 22] = 3 shifts: 6AM-2PM, 2PM-10PM, 10PM-6AM
    # [6, 12, 18, 0] = 4 shifts: 6AM-12PM, 12PM-6PM, 6PM-12AM, 12AM-6AM
    SHIFT_TIMES = [8, 14, 20]

    # Find the most recent shift change time
    current_hour = now.hour

    # Sort shift times to handle them in order
    sorted_shifts = sorted(SHIFT_TIMES)

    # Find the previous shift time
    previous_shift_hour = None
    previous_shift_date = now.date()

    # Look for the most recent shift time that has passed
    for shift_hour in reversed(sorted_shifts):
        if current_hour >= shift_hour:
            # Current time is after this shift time today
            previous_shift_hour = shift_hour
            break

    if previous_shift_hour is None:
        # Current time is before all shift times today, so use the last shift from yesterday
        previous_shift_hour = sorted_shifts[-1]
        previous_shift_date = (now - timedelta(days=1)).date()

    # Create the previous shift datetime
    previous_shift_time = datetime.combine(previous_shift_date, datetime.min.time().replace(hour=previous_shift_hour))
    previous_shift_time = pst.localize(previous_shift_time)

    # Format time for description
    def format_hour(hour):
        if hour == 0:
            return "12AM"
        elif hour < 12:
            return f"{hour}AM"
        elif hour == 12:
            return "12PM"
        else:
            return f"{hour-12}PM"

    # Create description
    if previous_shift_date == now.date():
        shift_description = f"Since {format_hour(previous_shift_hour)} PST"
    else:
        shift_description = f"Since {format_hour(previous_shift_hour)} PST Yesterday"

    # Convert back to naive datetime for Jira API compatibility
    previous_shift_naive = previous_shift_time.replace(tzinfo=None)

    return previous_shift_naive, shift_description


def handle_support_summary_command(command_text: str = "") -> Tuple[bool, str]:
    """
    Handle the /support-summary command.
    
    Args:
        command_text: The text following the /support-summary command
        
    Returns:
        tuple: (success: bool, response_message: str)
    """
    try:
        since_datetime, period_description = get_time_period_from_command(command_text)
        
        logger.info(f"Generating support summary for period: {period_description} (since {since_datetime})")
        
        client = JiraClient()
        
        projects = ["CRS"]
        issues = client.get_issues_modified_since(since_datetime, projects)
        
        if issues is None:
            error_msg = "❌ Error: Failed to retrieve issues from Jira. Please check the configuration."
            return False, error_msg
        
        # Format the summary
        summary = format_support_summary(issues, f"Support Summary {period_description}", SUPPORT_SUMMARY_MAX_ISSUES)
        
        return True, summary
        
    except Exception as e:
        logger.error(f"Error handling /support-summary command: {e}")
        traceback.print_exc()
        error_msg = "❌ Error: Failed to generate support summary"
        return False, error_msg
